import { Card, CardContent } from "@/components/ui/card";
import { Section, SectionHeader } from "@/components/ui/section";
import { 
  Wifi, 
  Car, 
  Coffee, 
  Utensils, 
  Dumbbell, 
  Waves, 
  Phone, 
  Shirt,
  Users,
  Shield,
  Clock,
  MapPin
} from "lucide-react";

const amenities = [
  {
    icon: Wifi,
    title: "Free Wi-Fi",
    description: "High-speed internet access throughout the hotel"
  },
  {
    icon: Car,
    title: "Free Parking",
    description: "Complimentary parking for all guests"
  },
  {
    icon: Phone,
    title: "24/7 Room Service",
    description: "Round-the-clock room service for your convenience"
  },
  {
    icon: Utensils,
    title: "Restaurant",
    description: "Fine dining with local and international cuisine"
  },
  {
    icon: Coffee,
    title: "Coffee Shop",
    description: "Fresh coffee and light snacks available all day"
  },
  {
    icon: Users,
    title: "Conference Hall",
    description: "Modern meeting facilities for business events"
  },
  {
    icon: Shirt,
    title: "Laundry Service",
    description: "Professional laundry and dry cleaning services"
  },
  {
    icon: MapPin,
    title: "Airport Transfer",
    description: "Convenient transportation to and from the airport"
  },
  {
    icon: Shield,
    title: "24/7 Security",
    description: "Round-the-clock security for your peace of mind"
  },
  {
    icon: Clock,
    title: "Concierge Service",
    description: "Personalized assistance for all your needs"
  },
  {
    icon: Dumbbell,
    title: "Fitness Center",
    description: "Modern gym equipment for your workout routine"
  },
  {
    icon: Waves,
    title: "Spa Services",
    description: "Relaxing spa treatments and wellness services"
  }
];

export function AmenitiesSection() {
  return (
    <Section className="bg-gray-50">
      <SectionHeader
        subtitle="Hotel Amenities"
        title="World-Class Facilities"
        description="Enjoy our comprehensive range of amenities designed to make your stay comfortable and memorable."
      />
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {amenities.map((amenity, index) => (
          <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white">
            <CardContent className="p-6 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4 group-hover:bg-primary/20 transition-colors">
                <amenity.icon className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {amenity.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {amenity.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </Section>
  );
}
