import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Section, SectionHeader } from "@/components/ui/section";
import { Star } from "lucide-react";

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    location: "Kathmandu, Nepal",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
    rating: 5,
    review: "Exceptional service and beautiful accommodations! The staff went above and beyond to make our business trip comfortable. The location is perfect for accessing Biratnagar's business district."
  },
  {
    id: 2,
    name: "<PERSON>",
    location: "London, UK",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
    rating: 5,
    review: "Hotel Himalaya exceeded all our expectations! The rooms are spacious and elegantly designed. The restaurant serves amazing local cuisine. Highly recommend for anyone visiting eastern Nepal."
  },
  {
    id: 3,
    name: "Priya Patel",
    location: "Mumbai, India",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    rating: 5,
    review: "A perfect blend of luxury and traditional hospitality. The spa services were incredible, and the mountain views from our suite were breathtaking. Will definitely return!"
  }
];

export function TestimonialsSection() {
  return (
    <Section className="bg-gray-50">
      <SectionHeader
        subtitle="Guest Reviews"
        title="What Our Guests Say"
        description="Read what our valued guests have to say about their experience at Hotel Himalaya."
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial) => (
          <Card key={testimonial.id} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <CardContent className="p-6">
              {/* Rating */}
              <div className="flex space-x-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              
              {/* Review */}
              <blockquote className="text-gray-600 mb-6 italic">
                "{testimonial.review}"
              </blockquote>
              
              {/* Author */}
              <div className="flex items-center space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                  <AvatarFallback>
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.location}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Additional testimonial stats */}
      <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
        <div>
          <div className="text-4xl font-bold text-primary mb-2">500+</div>
          <p className="text-gray-600">Happy Guests</p>
        </div>
        <div>
          <div className="text-4xl font-bold text-primary mb-2">4.9</div>
          <p className="text-gray-600">Average Rating</p>
        </div>
        <div>
          <div className="text-4xl font-bold text-primary mb-2">98%</div>
          <p className="text-gray-600">Satisfaction Rate</p>
        </div>
      </div>
    </Section>
  );
}
