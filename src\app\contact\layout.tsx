import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Contact Us - Hotel Himalaya Biratnagar",
  description: "Get in touch with Hotel Himalaya. Find our contact information, location, working hours, and send us a message. We're here to help make your stay perfect.",
  keywords: "contact Hotel Himalaya, Biratnagar hotel contact, hotel phone number, hotel address, Nepal hotel contact",
  openGraph: {
    title: "Contact Us - Hotel Himalaya Biratnagar",
    description: "Get in touch with Hotel Himalaya in Biratnagar, Nepal. We're here to help make your stay perfect.",
    type: "website",
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
