import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Section, SectionHeader } from "@/components/ui/section";
import { Wifi, Car, Coffee, Tv, Bath, Bed } from "lucide-react";

const featuredRooms = [
  {
    id: 1,
    name: "Deluxe Room",
    image: "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    price: "NPR 4,500",
    features: ["King Size Bed", "Free Wi-Fi", "Air Conditioning", "Private Bathroom"],
    amenities: [Bed, Wifi, Coffee, Bath],
    popular: true
  },
  {
    id: 2,
    name: "Executive Suite",
    image: "https://images.unsplash.com/photo-1618773928121-c32242e63f39?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    price: "NPR 8,500",
    features: ["Separate Living Area", "Mountain View", "Mini Bar", "Work Desk"],
    amenities: [Bed, Wifi, Tv, Car],
    popular: false
  },
  {
    id: 3,
    name: "Presidential Suite",
    image: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    price: "NPR 15,000",
    features: ["Panoramic City View", "Jacuzzi", "Butler Service", "Private Balcony"],
    amenities: [Bed, Wifi, Coffee, Bath],
    popular: false
  }
];

export function FeaturedRoomsSection() {
  return (
    <Section>
      <SectionHeader
        subtitle="Our Rooms"
        title="Featured Accommodations"
        description="Choose from our carefully designed rooms and suites, each offering comfort, elegance, and modern amenities."
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {featuredRooms.map((room) => (
          <Card key={room.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300">
            <div className="relative h-64 overflow-hidden">
              {room.popular && (
                <Badge className="absolute top-4 left-4 z-10 bg-primary">
                  Most Popular
                </Badge>
              )}
              <Image
                src={room.image}
                alt={room.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-gray-900">{room.name}</h3>
                <div className="text-right">
                  <p className="text-2xl font-bold text-primary">{room.price}</p>
                  <p className="text-sm text-gray-500">per night</p>
                </div>
              </div>
              
              <div className="flex space-x-2 mb-4">
                {room.amenities.map((Icon, index) => (
                  <div key={index} className="p-2 bg-gray-100 rounded-lg">
                    <Icon className="h-4 w-4 text-gray-600" />
                  </div>
                ))}
              </div>
              
              <ul className="space-y-2 mb-6">
                {room.features.map((feature, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center">
                    <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
              
              <Button asChild className="w-full">
                <Link href="/contact">
                  Call to Book
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="text-center mt-12">
        <Button asChild variant="outline" size="lg">
          <Link href="/rooms">
            View All Rooms
          </Link>
        </Button>
      </div>
    </Section>
  );
}
