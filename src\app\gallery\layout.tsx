import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Photo Gallery - Hotel Himalaya Biratnagar",
  description: "Explore our beautiful hotel through our photo gallery. View images of our rooms, facilities, dining areas, and stunning architecture at Hotel Himalaya, Biratnagar.",
  keywords: "hotel gallery, hotel photos, Biratnagar hotel images, Hotel Himalaya pictures, Nepal hotel gallery",
  openGraph: {
    title: "Photo Gallery - Hotel Himalaya Biratnagar",
    description: "Explore our beautiful hotel through our photo gallery at Hotel Himalaya, Biratnagar.",
    type: "website",
  },
};

export default function GalleryLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
