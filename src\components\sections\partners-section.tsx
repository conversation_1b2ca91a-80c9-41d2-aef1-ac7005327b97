import Image from "next/image";
import { Section, SectionHeader } from "@/components/ui/section";

const partners = [
  {
    name: "Booking.com",
    logo: "https://logos-world.net/wp-content/uploads/2021/08/Booking-Logo.png",
    width: 120,
    height: 40
  },
  {
    name: "<PERSON>go<PERSON>",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/Agoda-Logo.png",
    width: 100,
    height: 40
  },
  {
    name: "TripAdvisor",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/TripAdvisor-Logo.png",
    width: 120,
    height: 40
  },
  {
    name: "Expedia",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/Expedia-Logo.png",
    width: 110,
    height: 40
  },
  {
    name: "Hotels.com",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/Hotels-com-Logo.png",
    width: 120,
    height: 40
  },
  {
    name: "Nepal Tourism Board",
    logo: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiB2aWV3Qm94PSIwIDAgMTIwIDQwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMDA3QkZGIi8+Cjx0ZXh0IHg9IjYwIiB5PSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TmVwYWwgVG91cmlzbTwvdGV4dD4KPC9zdmc+",
    width: 120,
    height: 40
  }
];

export function PartnersSection() {
  return (
    <Section>
      <SectionHeader
        subtitle="Our Partners"
        title="Trusted by Leading Platforms"
        description="We're proud to partner with the world's leading travel and hospitality platforms to serve you better."
      />
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
        {partners.map((partner, index) => (
          <div 
            key={index} 
            className="flex items-center justify-center p-4 grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100"
          >
            <div className="relative">
              <Image
                src={partner.logo}
                alt={`${partner.name} logo`}
                width={partner.width}
                height={partner.height}
                className="object-contain"
              />
            </div>
          </div>
        ))}
      </div>
      
      {/* Trust indicators */}
      <div className="mt-16 bg-gray-50 rounded-2xl p-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">✓</div>
            <h3 className="font-semibold text-gray-900">Verified Hotel</h3>
            <p className="text-gray-600 text-sm">Certified by major booking platforms</p>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">★</div>
            <h3 className="font-semibold text-gray-900">Award Winning</h3>
            <p className="text-gray-600 text-sm">Recognized for excellence in hospitality</p>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">🛡️</div>
            <h3 className="font-semibold text-gray-900">Secure Booking</h3>
            <p className="text-gray-600 text-sm">Safe and secure reservation process</p>
          </div>
        </div>
      </div>
    </Section>
  );
}
