{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/hero-section.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight } from \"lucide-react\";\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div \n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: \"url('https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')\"\n        }}\n      />\n      \n      {/* Overlay */}\n      <div className=\"absolute inset-0 bg-black/40\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6\">\n            Hotel Himalaya\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 text-gray-200\">\n            Experience luxury and comfort in the heart of Biratnagar, Nepal\n          </p>\n          <p className=\"text-lg md:text-xl mb-12 text-gray-300 max-w-2xl mx-auto\">\n            Where traditional Nepalese hospitality meets modern elegance. \n            Your perfect destination for business and leisure.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button asChild size=\"lg\" className=\"text-lg px-8 py-6\">\n              <Link href=\"/rooms\">\n                Explore Rooms\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-6 bg-white/10 border-white/30 text-white hover:bg-white/20\">\n              <Link href=\"/gallery\">\n                View Gallery\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white/50 rounded-full mt-2\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBACnB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;oCAAK,WAAU;8CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;4CAAS;0DAElB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG1B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/ui/section.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\ninterface SectionProps {\n  children: React.ReactNode;\n  className?: string;\n  id?: string;\n}\n\nexport function Section({ children, className, id }: SectionProps) {\n  return (\n    <section id={id} className={cn(\"py-16 lg:py-24\", className)}>\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {children}\n      </div>\n    </section>\n  );\n}\n\ninterface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  className?: string;\n}\n\nexport function SectionHeader({ title, subtitle, description, className }: SectionHeaderProps) {\n  return (\n    <div className={cn(\"text-center mb-12\", className)}>\n      {subtitle && (\n        <p className=\"text-primary font-medium mb-2\">{subtitle}</p>\n      )}\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n        {title}\n      </h2>\n      {description && (\n        <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n          {description}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAQO,SAAS,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAgB;IAC/D,qBACE,8OAAC;QAAQ,IAAI;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAC/C,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AASO,SAAS,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAsB;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;;YACrC,0BACC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;0BAEhD,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/about-section.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\n\nexport function AboutSection() {\n  return (\n    <Section className=\"bg-gray-50\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n        <div>\n          <SectionHeader\n            subtitle=\"About Us\"\n            title=\"Welcome to Hotel Himalaya\"\n            description=\"Nestled in the vibrant city of Biratnagar, Hotel Himalaya stands as a beacon of luxury and comfort in eastern Nepal.\"\n            className=\"text-left mb-8\"\n          />\n          \n          <div className=\"space-y-6 text-gray-600\">\n            <p>\n              Since our establishment, we have been committed to providing exceptional hospitality \n              that reflects the warmth and culture of Nepal. Our hotel combines traditional Nepalese \n              architecture with modern amenities to create an unforgettable experience for our guests.\n            </p>\n            \n            <p>\n              Located in the commercial hub of Province 1, we offer easy access to business centers, \n              shopping districts, and cultural attractions. Whether you're here for business or leisure, \n              our dedicated team ensures your stay is comfortable and memorable.\n            </p>\n            \n            <p>\n              From our elegantly appointed rooms to our world-class dining options, every detail \n              has been carefully crafted to exceed your expectations. Experience the perfect blend \n              of luxury, comfort, and authentic Nepalese hospitality.\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"relative\">\n          <div className=\"relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl\">\n            <Image\n              src=\"https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80\"\n              alt=\"Hotel Himalaya lobby interior\"\n              fill\n              className=\"object-cover\"\n            />\n          </div>\n          \n          {/* Decorative elements */}\n          <div className=\"absolute -top-6 -left-6 w-24 h-24 bg-primary/10 rounded-full\"></div>\n          <div className=\"absolute -bottom-6 -right-6 w-32 h-32 bg-primary/5 rounded-full\"></div>\n        </div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,UAAS;4BACT,OAAM;4BACN,aAAY;4BACZ,WAAU;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CAMH,8OAAC;8CAAE;;;;;;8CAMH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,WAAU;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/featured-rooms-section.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { Wifi, Car, Coffee, Tv, Bath, Bed } from \"lucide-react\";\n\nconst featuredRooms = [\n  {\n    id: 1,\n    name: \"Deluxe Room\",\n    image: \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    price: \"NPR 4,500\",\n    features: [\"King Size Bed\", \"Free Wi-Fi\", \"Air Conditioning\", \"Private Bathroom\"],\n    amenities: [Bed, Wifi, Coffee, Bath],\n    popular: true\n  },\n  {\n    id: 2,\n    name: \"Executive Suite\",\n    image: \"https://images.unsplash.com/photo-1618773928121-c32242e63f39?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    price: \"NPR 8,500\",\n    features: [\"Separate Living Area\", \"Mountain View\", \"Mini Bar\", \"Work Desk\"],\n    amenities: [Bed, Wifi, Tv, Car],\n    popular: false\n  },\n  {\n    id: 3,\n    name: \"Presidential Suite\",\n    image: \"https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    price: \"NPR 15,000\",\n    features: [\"Panoramic City View\", \"Jacuzzi\", \"Butler Service\", \"Private Balcony\"],\n    amenities: [Bed, Wifi, Coffee, Bath],\n    popular: false\n  }\n];\n\nexport function FeaturedRoomsSection() {\n  return (\n    <Section>\n      <SectionHeader\n        subtitle=\"Our Rooms\"\n        title=\"Featured Accommodations\"\n        description=\"Choose from our carefully designed rooms and suites, each offering comfort, elegance, and modern amenities.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {featuredRooms.map((room) => (\n          <Card key={room.id} className=\"group overflow-hidden hover:shadow-xl transition-all duration-300\">\n            <div className=\"relative h-64 overflow-hidden\">\n              {room.popular && (\n                <Badge className=\"absolute top-4 left-4 z-10 bg-primary\">\n                  Most Popular\n                </Badge>\n              )}\n              <Image\n                src={room.image}\n                alt={room.name}\n                fill\n                className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n              />\n            </div>\n            \n            <CardContent className=\"p-6\">\n              <div className=\"flex justify-between items-start mb-4\">\n                <h3 className=\"text-xl font-semibold text-gray-900\">{room.name}</h3>\n                <div className=\"text-right\">\n                  <p className=\"text-2xl font-bold text-primary\">{room.price}</p>\n                  <p className=\"text-sm text-gray-500\">per night</p>\n                </div>\n              </div>\n              \n              <div className=\"flex space-x-2 mb-4\">\n                {room.amenities.map((Icon, index) => (\n                  <div key={index} className=\"p-2 bg-gray-100 rounded-lg\">\n                    <Icon className=\"h-4 w-4 text-gray-600\" />\n                  </div>\n                ))}\n              </div>\n              \n              <ul className=\"space-y-2 mb-6\">\n                {room.features.map((feature, index) => (\n                  <li key={index} className=\"text-sm text-gray-600 flex items-center\">\n                    <span className=\"w-1.5 h-1.5 bg-primary rounded-full mr-2\"></span>\n                    {feature}\n                  </li>\n                ))}\n              </ul>\n              \n              <Button asChild className=\"w-full\">\n                <Link href=\"/contact\">\n                  Call to Book\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n      \n      <div className=\"text-center mt-12\">\n        <Button asChild variant=\"outline\" size=\"lg\">\n          <Link href=\"/rooms\">\n            View All Rooms\n          </Link>\n        </Button>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAEA,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;YAAC;YAAiB;YAAc;YAAoB;SAAmB;QACjF,WAAW;YAAC,gMAAA,CAAA,MAAG;YAAE,kMAAA,CAAA,OAAI;YAAE,sMAAA,CAAA,SAAM;YAAE,kMAAA,CAAA,OAAI;SAAC;QACpC,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;YAAC;YAAwB;YAAiB;YAAY;SAAY;QAC5E,WAAW;YAAC,gMAAA,CAAA,MAAG;YAAE,kMAAA,CAAA,OAAI;YAAE,8LAAA,CAAA,KAAE;YAAE,gMAAA,CAAA,MAAG;SAAC;QAC/B,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;YAAC;YAAuB;YAAW;YAAkB;SAAkB;QACjF,WAAW;YAAC,gMAAA,CAAA,MAAG;YAAE,kMAAA,CAAA,OAAI;YAAE,sMAAA,CAAA,SAAM;YAAE,kMAAA,CAAA,OAAI;SAAC;QACpC,SAAS;IACX;CACD;AAEM,SAAS;IACd,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,gIAAA,CAAA,OAAI;wBAAe,WAAU;;0CAC5B,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,OAAO,kBACX,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAwC;;;;;;kDAI3D,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,KAAK;wCACf,KAAK,KAAK,IAAI;wCACd,IAAI;wCACJ,WAAU;;;;;;;;;;;;0CAId,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC,KAAK,IAAI;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAmC,KAAK,KAAK;;;;;;kEAC1D,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;gDAAgB,WAAU;0DACzB,cAAA,8OAAC;oDAAK,WAAU;;;;;;+CADR;;;;;;;;;;kDAMd,8OAAC;wCAAG,WAAU;kDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC;wDAAK,WAAU;;;;;;oDACf;;+CAFM;;;;;;;;;;kDAOb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;;;;;;;;uBA1CjB,KAAK,EAAE;;;;;;;;;;0BAmDtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,SAAQ;oBAAU,MAAK;8BACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAS;;;;;;;;;;;;;;;;;;;;;;AAO9B", "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/amenities-section.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { \n  Wifi, \n  Car, \n  Coffee, \n  Utensils, \n  Dumbbell, \n  Waves, \n  Phone, \n  Shirt,\n  Users,\n  Shield,\n  Clock,\n  MapPin\n} from \"lucide-react\";\n\nconst amenities = [\n  {\n    icon: Wifi,\n    title: \"Free Wi-Fi\",\n    description: \"High-speed internet access throughout the hotel\"\n  },\n  {\n    icon: Car,\n    title: \"Free Parking\",\n    description: \"Complimentary parking for all guests\"\n  },\n  {\n    icon: Phone,\n    title: \"24/7 Room Service\",\n    description: \"Round-the-clock room service for your convenience\"\n  },\n  {\n    icon: Utensils,\n    title: \"Restaurant\",\n    description: \"Fine dining with local and international cuisine\"\n  },\n  {\n    icon: Coffee,\n    title: \"Coffee Shop\",\n    description: \"Fresh coffee and light snacks available all day\"\n  },\n  {\n    icon: Users,\n    title: \"Conference Hall\",\n    description: \"Modern meeting facilities for business events\"\n  },\n  {\n    icon: Shirt,\n    title: \"Laundry Service\",\n    description: \"Professional laundry and dry cleaning services\"\n  },\n  {\n    icon: MapPin,\n    title: \"Airport Transfer\",\n    description: \"Convenient transportation to and from the airport\"\n  },\n  {\n    icon: Shield,\n    title: \"24/7 Security\",\n    description: \"Round-the-clock security for your peace of mind\"\n  },\n  {\n    icon: Clock,\n    title: \"Concierge Service\",\n    description: \"Personalized assistance for all your needs\"\n  },\n  {\n    icon: Dumbbell,\n    title: \"Fitness Center\",\n    description: \"Modern gym equipment for your workout routine\"\n  },\n  {\n    icon: Waves,\n    title: \"Spa Services\",\n    description: \"Relaxing spa treatments and wellness services\"\n  }\n];\n\nexport function AmenitiesSection() {\n  return (\n    <Section className=\"bg-gray-50\">\n      <SectionHeader\n        subtitle=\"Hotel Amenities\"\n        title=\"World-Class Facilities\"\n        description=\"Enjoy our comprehensive range of amenities designed to make your stay comfortable and memorable.\"\n      />\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {amenities.map((amenity, index) => (\n          <Card key={index} className=\"group hover:shadow-lg transition-all duration-300 border-0 bg-white\">\n            <CardContent className=\"p-6 text-center\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4 group-hover:bg-primary/20 transition-colors\">\n                <amenity.icon className=\"h-8 w-8 text-primary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {amenity.title}\n              </h3>\n              <p className=\"text-gray-600 text-sm\">\n                {amenity.description}\n              </p>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAeA,MAAM,YAAY;IAChB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;;0BACjB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;kCAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,QAAQ,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;;;;;;uBATf;;;;;;;;;;;;;;;;AAiBrB", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/gallery-preview-section.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\n\nconst galleryImages = [\n  {\n    src: \"https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Hotel exterior view\",\n    title: \"Hotel Exterior\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80\",\n    alt: \"Elegant hotel lobby\",\n    title: \"Lobby\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Luxurious hotel room\",\n    title: \"Deluxe Room\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Hotel restaurant dining area\",\n    title: \"Restaurant\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n    alt: \"Presidential suite interior\",\n    title: \"Presidential Suite\"\n  },\n  {\n    src: \"https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80\",\n    alt: \"Hotel spa and wellness center\",\n    title: \"Spa & Wellness\"\n  }\n];\n\nexport function GalleryPreviewSection() {\n  return (\n    <Section>\n      <SectionHeader\n        subtitle=\"Gallery\"\n        title=\"Explore Our Hotel\"\n        description=\"Take a visual tour of our beautiful facilities, elegant rooms, and stunning spaces.\"\n      />\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {galleryImages.map((image, index) => (\n          <div \n            key={index} \n            className={`group relative overflow-hidden rounded-xl cursor-pointer ${\n              index === 0 ? 'sm:col-span-2 sm:row-span-2' : ''\n            }`}\n          >\n            <div className={`relative ${index === 0 ? 'h-96 lg:h-[500px]' : 'h-48 lg:h-64'}`}>\n              <Image\n                src={image.src}\n                alt={image.alt}\n                fill\n                className=\"object-cover group-hover:scale-105 transition-transform duration-500\"\n              />\n              \n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300\" />\n              \n              {/* Title */}\n              <div className=\"absolute bottom-4 left-4 text-white\">\n                <h3 className=\"text-lg font-semibold\">{image.title}</h3>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"text-center mt-12\">\n        <Button asChild size=\"lg\">\n          <Link href=\"/gallery\">\n            View Full Gallery\n          </Link>\n        </Button>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB;IACpB;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;wBAEC,WAAW,CAAC,yDAAyD,EACnE,UAAU,IAAI,gCAAgC,IAC9C;kCAEF,cAAA,8OAAC;4BAAI,WAAW,CAAC,SAAS,EAAE,UAAU,IAAI,sBAAsB,gBAAgB;;8CAC9E,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,GAAG;oCACd,KAAK,MAAM,GAAG;oCACd,IAAI;oCACJ,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAyB,MAAM,KAAK;;;;;;;;;;;;;;;;;uBAlBjD;;;;;;;;;;0BAyBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,MAAK;8BACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAW;;;;;;;;;;;;;;;;;;;;;;AAOhC", "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0CACA", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/testimonials-section.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\nimport { Star } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON><PERSON>\",\n    location: \"Kathmandu, Nepal\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n    rating: 5,\n    review: \"Exceptional service and beautiful accommodations! The staff went above and beyond to make our business trip comfortable. The location is perfect for accessing Biratnagar's business district.\"\n  },\n  {\n    id: 2,\n    name: \"<PERSON>\",\n    location: \"London, UK\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n    rating: 5,\n    review: \"Hotel Himalaya exceeded all our expectations! The rooms are spacious and elegantly designed. The restaurant serves amazing local cuisine. Highly recommend for anyone visiting eastern Nepal.\"\n  },\n  {\n    id: 3,\n    name: \"Priya Patel\",\n    location: \"Mumbai, India\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80\",\n    rating: 5,\n    review: \"A perfect blend of luxury and traditional hospitality. The spa services were incredible, and the mountain views from our suite were breathtaking. Will definitely return!\"\n  }\n];\n\nexport function TestimonialsSection() {\n  return (\n    <Section className=\"bg-gray-50\">\n      <SectionHeader\n        subtitle=\"Guest Reviews\"\n        title=\"What Our Guests Say\"\n        description=\"Read what our valued guests have to say about their experience at Hotel Himalaya.\"\n      />\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {testimonials.map((testimonial) => (\n          <Card key={testimonial.id} className=\"border-0 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n            <CardContent className=\"p-6\">\n              {/* Rating */}\n              <div className=\"flex space-x-1 mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <Star key={i} className=\"h-5 w-5 fill-yellow-400 text-yellow-400\" />\n                ))}\n              </div>\n              \n              {/* Review */}\n              <blockquote className=\"text-gray-600 mb-6 italic\">\n                \"{testimonial.review}\"\n              </blockquote>\n              \n              {/* Author */}\n              <div className=\"flex items-center space-x-4\">\n                <Avatar className=\"h-12 w-12\">\n                  <AvatarImage src={testimonial.avatar} alt={testimonial.name} />\n                  <AvatarFallback>\n                    {testimonial.name.split(' ').map(n => n[0]).join('')}\n                  </AvatarFallback>\n                </Avatar>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">{testimonial.name}</p>\n                  <p className=\"text-sm text-gray-500\">{testimonial.location}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n      \n      {/* Additional testimonial stats */}\n      <div className=\"mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 text-center\">\n        <div>\n          <div className=\"text-4xl font-bold text-primary mb-2\">500+</div>\n          <p className=\"text-gray-600\">Happy Guests</p>\n        </div>\n        <div>\n          <div className=\"text-4xl font-bold text-primary mb-2\">4.9</div>\n          <p className=\"text-gray-600\">Average Rating</p>\n        </div>\n        <div>\n          <div className=\"text-4xl font-bold text-primary mb-2\">98%</div>\n          <p className=\"text-gray-600\">Satisfaction Rate</p>\n        </div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;CACD;AAEM,SAAS;IACd,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;;0BACjB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC,gIAAA,CAAA,OAAI;wBAAsB,WAAU;kCACnC,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAKf,8OAAC;oCAAW,WAAU;;wCAA4B;wCAC9C,YAAY,MAAM;wCAAC;;;;;;;8CAIvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,YAAY,MAAM;oDAAE,KAAK,YAAY,IAAI;;;;;;8DAC3D,8OAAC,kIAAA,CAAA,iBAAc;8DACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sDAGrD,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA+B,YAAY,IAAI;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAAyB,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;uBAxBvD,YAAY,EAAE;;;;;;;;;;0BAiC7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CAAuC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CAAuC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CAAuC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/partners-section.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport { Section, SectionHeader } from \"@/components/ui/section\";\n\nconst partners = [\n  {\n    name: \"Booking.com\",\n    logo: \"https://logos-world.net/wp-content/uploads/2021/08/Booking-Logo.png\",\n    width: 120,\n    height: 40\n  },\n  {\n    name: \"<PERSON>go<PERSON>\",\n    logo: \"https://logos-world.net/wp-content/uploads/2021/02/Agoda-Logo.png\",\n    width: 100,\n    height: 40\n  },\n  {\n    name: \"TripAdvisor\",\n    logo: \"https://logos-world.net/wp-content/uploads/2021/02/TripAdvisor-Logo.png\",\n    width: 120,\n    height: 40\n  },\n  {\n    name: \"Expedia\",\n    logo: \"https://logos-world.net/wp-content/uploads/2021/02/Expedia-Logo.png\",\n    width: 110,\n    height: 40\n  },\n  {\n    name: \"Hotels.com\",\n    logo: \"https://logos-world.net/wp-content/uploads/2021/02/Hotels-com-Logo.png\",\n    width: 120,\n    height: 40\n  },\n  {\n    name: \"Nepal Tourism Board\",\n    logo: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiB2aWV3Qm94PSIwIDAgMTIwIDQwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMDA3QkZGIi8+Cjx0ZXh0IHg9IjYwIiB5PSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TmVwYWwgVG91cmlzbTwvdGV4dD4KPC9zdmc+\",\n    width: 120,\n    height: 40\n  }\n];\n\nexport function PartnersSection() {\n  return (\n    <Section>\n      <SectionHeader\n        subtitle=\"Our Partners\"\n        title=\"Trusted by Leading Platforms\"\n        description=\"We're proud to partner with the world's leading travel and hospitality platforms to serve you better.\"\n      />\n      \n      <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center\">\n        {partners.map((partner, index) => (\n          <div \n            key={index} \n            className=\"flex items-center justify-center p-4 grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100\"\n          >\n            <div className=\"relative\">\n              <Image\n                src={partner.logo}\n                alt={`${partner.name} logo`}\n                width={partner.width}\n                height={partner.height}\n                className=\"object-contain\"\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {/* Trust indicators */}\n      <div className=\"mt-16 bg-gray-50 rounded-2xl p-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\">\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl font-bold text-primary\">✓</div>\n            <h3 className=\"font-semibold text-gray-900\">Verified Hotel</h3>\n            <p className=\"text-gray-600 text-sm\">Certified by major booking platforms</p>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl font-bold text-primary\">★</div>\n            <h3 className=\"font-semibold text-gray-900\">Award Winning</h3>\n            <p className=\"text-gray-600 text-sm\">Recognized for excellence in hospitality</p>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl font-bold text-primary\">🛡️</div>\n            <h3 className=\"font-semibold text-gray-900\">Secure Booking</h3>\n            <p className=\"text-gray-600 text-sm\">Safe and secure reservation process</p>\n          </div>\n        </div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;IACV;CACD;AAEM,SAAS;IACd,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wBAEC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,IAAI;gCACjB,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC;gCAC3B,OAAO,QAAQ,KAAK;gCACpB,QAAQ,QAAQ,MAAM;gCACtB,WAAU;;;;;;;;;;;uBATT;;;;;;;;;;0BAiBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAkC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAkC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAkC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/contact-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactSection() from the server but ContactSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/contact-section.tsx <module evaluation>\",\n    \"ContactSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,6EACA", "debugId": null}}, {"offset": {"line": 1688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/components/sections/contact-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactSection() from the server but ContactSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/contact-section.tsx\",\n    \"ContactSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA", "debugId": null}}, {"offset": {"line": 1700, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/hotel/src/app/page.tsx"], "sourcesContent": ["import { HeroSection } from \"@/components/sections/hero-section\";\nimport { AboutSection } from \"@/components/sections/about-section\";\nimport { FeaturedRoomsSection } from \"@/components/sections/featured-rooms-section\";\nimport { AmenitiesSection } from \"@/components/sections/amenities-section\";\nimport { GalleryPreviewSection } from \"@/components/sections/gallery-preview-section\";\nimport { TestimonialsSection } from \"@/components/sections/testimonials-section\";\nimport { PartnersSection } from \"@/components/sections/partners-section\";\nimport { ContactSection } from \"@/components/sections/contact-section\";\n\nexport default function Home() {\n  return (\n    <>\n      <HeroSection />\n      <AboutSection />\n      <FeaturedRoomsSection />\n      <AmenitiesSection />\n      <GalleryPreviewSection />\n      <TestimonialsSection />\n      <PartnersSection />\n      <ContactSection />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,iJAAA,CAAA,cAAW;;;;;0BACZ,8OAAC,kJAAA,CAAA,eAAY;;;;;0BACb,8OAAC,8JAAA,CAAA,uBAAoB;;;;;0BACrB,8OAAC,sJAAA,CAAA,mBAAgB;;;;;0BACjB,8OAAC,+JAAA,CAAA,wBAAqB;;;;;0BACtB,8OAAC,yJAAA,CAAA,sBAAmB;;;;;0BACpB,8OAAC,qJAAA,CAAA,kBAAe;;;;;0BAChB,8OAAC,oJAAA,CAAA,iBAAc;;;;;;;AAGrB", "debugId": null}}]}