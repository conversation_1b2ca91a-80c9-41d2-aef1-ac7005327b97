import type { <PERSON>ada<PERSON> } from "next";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Section, SectionHeader } from "@/components/ui/section";
import { Wifi, Car, Coffee, Tv, Bath, Bed, Users, Mountain, Utensils, Phone } from "lucide-react";

export const metadata: Metadata = {
  title: "Rooms & Suites - Hotel Himalaya Biratnagar",
  description: "Discover our luxurious rooms and suites at Hotel Himalaya. From standard rooms to presidential suites, find the perfect accommodation in Biratnagar, Nepal.",
  keywords: "hotel rooms, suites, Biratnagar accommodation, luxury rooms, Nepal hotel rooms",
  openGraph: {
    title: "Rooms & Suites - Hotel Himalaya Biratnagar",
    description: "Discover our luxurious rooms and suites at Hotel Himalaya in Biratnagar, Nepal.",
    type: "website",
  },
};

const rooms = [
  {
    id: 1,
    name: "Standard Room",
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2058&q=80",
    price: "NPR 3,500",
    size: "25 sqm",
    occupancy: "2 Adults",
    features: ["Queen Size Bed", "Free Wi-Fi", "Air Conditioning", "Private Bathroom", "Work Desk"],
    amenities: [Bed, Wifi, Coffee, Bath, Tv],
    description: "Comfortable and well-appointed room perfect for business travelers and couples."
  },
  {
    id: 2,
    name: "Deluxe Room",
    image: "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    price: "NPR 4,500",
    size: "30 sqm",
    occupancy: "2 Adults",
    features: ["King Size Bed", "Free Wi-Fi", "Air Conditioning", "Private Bathroom", "Mini Fridge", "City View"],
    amenities: [Bed, Wifi, Coffee, Bath, Tv, Car],
    description: "Spacious room with modern amenities and beautiful city views.",
    popular: true
  },
  {
    id: 3,
    name: "Superior Room",
    image: "https://images.unsplash.com/photo-1618773928121-c32242e63f39?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    price: "NPR 6,000",
    size: "35 sqm",
    occupancy: "2-3 Adults",
    features: ["King Size Bed", "Seating Area", "Mountain View", "Mini Bar", "Premium Bathroom", "Balcony"],
    amenities: [Bed, Wifi, Mountain, Bath, Tv, Utensils],
    description: "Enhanced comfort with stunning mountain views and premium amenities."
  },
  {
    id: 4,
    name: "Executive Suite",
    image: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    price: "NPR 8,500",
    size: "50 sqm",
    occupancy: "2-4 Adults",
    features: ["Separate Living Area", "King Size Bed", "Work Desk", "Mini Bar", "Mountain View", "Premium Bathroom"],
    amenities: [Bed, Wifi, Tv, Car, Mountain, Phone],
    description: "Elegant suite with separate living area, perfect for extended stays and business meetings."
  },
  {
    id: 5,
    name: "Family Suite",
    image: "https://images.unsplash.com/photo-1590490360182-c33d57733427?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80",
    price: "NPR 10,000",
    size: "60 sqm",
    occupancy: "4-6 Adults",
    features: ["Two Bedrooms", "Living Area", "Kitchenette", "Two Bathrooms", "Balcony", "Family Amenities"],
    amenities: [Bed, Users, Wifi, Utensils, Bath, Tv],
    description: "Spacious family accommodation with two bedrooms and living area."
  },
  {
    id: 6,
    name: "Presidential Suite",
    image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80",
    price: "NPR 15,000",
    size: "80 sqm",
    occupancy: "2-4 Adults",
    features: ["Panoramic City View", "Jacuzzi", "Butler Service", "Private Balcony", "Premium Furnishing", "VIP Amenities"],
    amenities: [Bed, Wifi, Mountain, Bath, Phone, Car],
    description: "Ultimate luxury experience with panoramic views and exclusive services.",
    premium: true
  }
];

export default function RoomsPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative h-64 md:h-80 flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
          }}
        />
        <div className="absolute inset-0 bg-black/50" />
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Rooms & Suites</h1>
          <p className="text-xl text-gray-200">Discover comfort and luxury in every room</p>
        </div>
      </section>

      {/* Rooms Grid */}
      <Section>
        <SectionHeader
          title="Choose Your Perfect Room"
          description="From comfortable standard rooms to luxurious presidential suites, we have the perfect accommodation for every need and budget."
        />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {rooms.map((room) => (
            <Card key={room.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300">
              <div className="md:flex">
                <div className="relative md:w-1/2 h-64 md:h-auto overflow-hidden">
                  {room.popular && (
                    <Badge className="absolute top-4 left-4 z-10 bg-primary">
                      Most Popular
                    </Badge>
                  )}
                  {room.premium && (
                    <Badge className="absolute top-4 left-4 z-10 bg-yellow-500">
                      Premium
                    </Badge>
                  )}
                  <Image
                    src={room.image}
                    alt={room.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                
                <CardContent className="md:w-1/2 p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">{room.name}</h3>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-primary">{room.price}</p>
                      <p className="text-sm text-gray-500">per night</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Size:</span> {room.size}
                    </div>
                    <div>
                      <span className="font-medium">Occupancy:</span> {room.occupancy}
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4 text-sm">{room.description}</p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {room.amenities.map((Icon, index) => (
                      <div key={index} className="p-2 bg-gray-100 rounded-lg">
                        <Icon className="h-4 w-4 text-gray-600" />
                      </div>
                    ))}
                  </div>
                  
                  <ul className="space-y-1 mb-6 text-sm">
                    {room.features.slice(0, 4).map((feature, index) => (
                      <li key={index} className="text-gray-600 flex items-center">
                        <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                    {room.features.length > 4 && (
                      <li className="text-gray-500 text-xs">
                        +{room.features.length - 4} more amenities
                      </li>
                    )}
                  </ul>
                  
                  <Button asChild className="w-full">
                    <Link href="/contact">
                      Book Now
                    </Link>
                  </Button>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
        
        {/* Additional Information */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Room Policies & Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Check-in</h4>
              <p className="text-gray-600">2:00 PM onwards</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Check-out</h4>
              <p className="text-gray-600">12:00 PM</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Cancellation</h4>
              <p className="text-gray-600">Free until 24 hours</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Payment</h4>
              <p className="text-gray-600">Cash & Card accepted</p>
            </div>
          </div>
        </div>
      </Section>
    </>
  );
}
