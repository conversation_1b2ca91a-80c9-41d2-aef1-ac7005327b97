import Image from "next/image";
import { Section, SectionHeader } from "@/components/ui/section";

export function AboutSection() {
  return (
    <Section className="bg-gray-50">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <SectionHeader
            subtitle="About Us"
            title="Welcome to Hotel Himalaya"
            description="Nestled in the vibrant city of Biratnagar, Hotel Himalaya stands as a beacon of luxury and comfort in eastern Nepal."
            className="text-left mb-8"
          />
          
          <div className="space-y-6 text-gray-600">
            <p>
              Since our establishment, we have been committed to providing exceptional hospitality 
              that reflects the warmth and culture of Nepal. Our hotel combines traditional Nepalese 
              architecture with modern amenities to create an unforgettable experience for our guests.
            </p>
            
            <p>
              Located in the commercial hub of Province 1, we offer easy access to business centers, 
              shopping districts, and cultural attractions. Whether you're here for business or leisure, 
              our dedicated team ensures your stay is comfortable and memorable.
            </p>
            
            <p>
              From our elegantly appointed rooms to our world-class dining options, every detail 
              has been carefully crafted to exceed your expectations. Experience the perfect blend 
              of luxury, comfort, and authentic Nepalese hospitality.
            </p>
          </div>
        </div>
        
        <div className="relative">
          <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
            <Image
              src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80"
              alt="Hotel Himalaya lobby interior"
              fill
              className="object-cover"
            />
          </div>
          
          {/* Decorative elements */}
          <div className="absolute -top-6 -left-6 w-24 h-24 bg-primary/10 rounded-full"></div>
          <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-primary/5 rounded-full"></div>
        </div>
      </div>
    </Section>
  );
}
