import type { <PERSON>ada<PERSON> } from "next";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Section, SectionHeader } from "@/components/ui/section";
import { Badge } from "@/components/ui/badge";
import { MapPin, Phone, Mail, Clock, Users, Award, Heart, Shield } from "lucide-react";

export const metadata: Metadata = {
  title: "About Us - Hotel Himalaya Biratnagar",
  description: "Learn about Hotel Himalaya's story, values, and commitment to excellence. Discover our team, location, and what makes us the premier hotel in Biratnagar, Nepal.",
  keywords: "about Hotel Himalaya, Biratnagar hotel story, Nepal hospitality, hotel team, hotel values",
  openGraph: {
    title: "About Us - Hotel Himalaya Biratnagar",
    description: "Learn about Hotel Himalaya's story and commitment to excellence in Biratnagar, Nepal.",
    type: "website",
  },
};

const values = [
  {
    icon: Heart,
    title: "Hospitality",
    description: "We believe in genuine Nepalese hospitality, treating every guest as family with warmth and care."
  },
  {
    icon: Award,
    title: "Excellence",
    description: "We strive for excellence in every service, ensuring the highest standards of quality and comfort."
  },
  {
    icon: Users,
    title: "Community",
    description: "We are committed to supporting our local community and preserving Nepalese culture and traditions."
  },
  {
    icon: Shield,
    title: "Trust",
    description: "We build lasting relationships with our guests through transparency, reliability, and consistent service."
  }
];

const teamMembers = [
  {
    name: "Ram Bahadur Thapa",
    position: "General Manager",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    experience: "15+ years in hospitality"
  },
  {
    name: "Sita Devi Sharma",
    position: "Operations Manager",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
    experience: "12+ years in hotel operations"
  },
  {
    name: "Bikash Rai",
    position: "Head Chef",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
    experience: "10+ years culinary expertise"
  }
];

export default function AboutPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative h-64 md:h-80 flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
          }}
        />
        <div className="absolute inset-0 bg-black/50" />
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">About Hotel Himalaya</h1>
          <p className="text-xl text-gray-200">Discover our story, values, and commitment to excellence</p>
        </div>
      </section>

      {/* Our Story */}
      <Section>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <SectionHeader
              subtitle="Our Story"
              title="A Legacy of Hospitality"
              description="Hotel Himalaya has been a cornerstone of luxury hospitality in Biratnagar since our establishment."
              className="text-left mb-8"
            />
            
            <div className="space-y-6 text-gray-600">
              <p>
                Founded with a vision to provide world-class hospitality in eastern Nepal, Hotel Himalaya 
                has grown to become the premier destination for discerning travelers. Our journey began 
                with a simple belief: that every guest deserves an exceptional experience that reflects 
                the beauty and warmth of Nepalese culture.
              </p>
              
              <p>
                Located in the heart of Biratnagar, we have witnessed the city's transformation into a 
                major commercial hub while maintaining our commitment to traditional values and modern 
                comfort. Our hotel serves as a bridge between Nepal's rich heritage and contemporary luxury.
              </p>
              
              <p>
                Today, we continue to set new standards in hospitality, combining authentic Nepalese 
                warmth with international service excellence. Every member of our team is dedicated to 
                creating memorable experiences that exceed our guests' expectations.
              </p>
            </div>
          </div>
          
          <div className="relative">
            <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80"
                alt="Hotel Himalaya heritage"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </Section>

      {/* Our Values */}
      <Section className="bg-gray-50">
        <SectionHeader
          subtitle="Our Values"
          title="What We Stand For"
          description="Our core values guide everything we do, from the way we welcome guests to how we serve our community."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => (
            <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                  <value.icon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600">
                  {value.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Our Team */}
      <Section>
        <SectionHeader
          subtitle="Our Team"
          title="Meet Our Leadership"
          description="Our experienced team is dedicated to providing exceptional service and creating memorable experiences."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="relative w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden">
                  <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-primary font-medium mb-2">
                  {member.position}
                </p>
                <Badge variant="secondary" className="text-xs">
                  {member.experience}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Location & Contact */}
      <Section className="bg-gray-50">
        <SectionHeader
          subtitle="Visit Us"
          title="Our Location"
          description="Conveniently located in the heart of Biratnagar, we're easily accessible from all major attractions and business centers."
        />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <Card className="border-0 shadow-lg">
            <CardContent className="p-8">
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">Contact Information</h3>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <MapPin className="h-6 w-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Address</h4>
                    <p className="text-gray-600">
                      Main Road, Biratnagar<br />
                      Province 1, Nepal<br />
                      Postal Code: 56613
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <Phone className="h-6 w-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Phone</h4>
                    <p className="text-gray-600">+977-21-123456</p>
                    <p className="text-gray-600">+977-98-12345678 (Mobile)</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <Mail className="h-6 w-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Email</h4>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <Clock className="h-6 w-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Reception Hours</h4>
                    <p className="text-gray-600">24/7 Available</p>
                    <p className="text-gray-600 text-sm">Check-in: 2:00 PM | Check-out: 12:00 PM</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Map */}
          <Card className="border-0 shadow-lg">
            <CardContent className="p-0">
              <div className="h-96 bg-gray-200 rounded-lg overflow-hidden">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3570.8947!2d87.2718!3d26.4525!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ef7c5c8f8f8f8f%3A0x8f8f8f8f8f8f8f8f!2sBiratnagar%2C%20Nepal!5e0!3m2!1sen!2sus!4v1234567890"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Hotel Himalaya Location"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </Section>
    </>
  );
}
